@push('scripts')
    <script src="{{ asset('assets/js/markerclusterer.js') }}"></script>


    <script>
        let map;
        let markers = [];
        let vehicleData = [];
        let selectedVehicleImei;
        let seletedVehicleIcon;
        let selectedVehicleLat;
        let selectedVehicleLng;
        let markerCluster;

        let currentGeofences;

        let directionsService;
        let allRoutes = []; // Store all route polylines
        let activePolyline = null; // Store the currently selected route
        let allMarkers = []; // Store all markers

        let drawnGeofences = [];


        let startRoute = null;
        let endRoute = null;
        let stopsRoute = [];

        // Initialize the map asynchronously
        async function initializeMap() {
            // Check if the HTML tag has the 'dark' class
            const isDarkMode = document.documentElement.classList.contains('dark');


            const mapCenter = {
                lat: 41.9028,
                lng: 12.4964
            }; // Initial center of the map

            // Load Google Maps API and libraries
            map = new google.maps.Map(document.getElementById('map'), {
                center: mapCenter,
                zoom: 6,
                mapId: "fleetMap", // Replace with your actual Map ID if applicable
                mapTypeId: isDarkMode ? google.maps.MapTypeId.HYBRID : google.maps.MapTypeId
                    .ROADMAP, // Use hybrid in dark mode for satellite with labels
            });

            directionsService = new google.maps.DirectionsService();

            const {
                AdvancedMarkerElement,
                PinElement
            } = await google.maps.importLibrary("marker");


            // Initial marker update
            updateMarkers(@json($devices), AdvancedMarkerElement);
        }


        const emptySelected = () => {

            selectedVehicleImei = null;

            // Remove existing route paths & markers
            // allRoutes.forEach(route => route.setMap(null));
            // allRoutes = [];
            // allMarkers.forEach(marker => marker.setMap(null));
            // allMarkers = [];
        }

        // Initialize marker clustering
        // Initialize marker clustering
        function initializeMarkerCluster() {
            if (markerCluster) {
                markerCluster.clearMarkers(); // Clear existing clusters
            }

            markerCluster = new markerClusterer.MarkerClusterer({
                markers: markers,
                map: map,
                algorithm: new markerClusterer.SuperClusterAlgorithm({
                    radius: 100,
                    maxZoom: 15,
                    minPoints: 2
                }),
                renderer: {
                    render: ({
                        count,
                        position
                    }) => {
                        // Determine cluster size and color
                        let size, backgroundColor, pulseSize;
                        if (count < 10) {
                            size = '40px';
                            pulseSize = '44px';
                            backgroundColor = '#F54619';
                        } else if (count < 50) {
                            size = '50px';
                            pulseSize = '54px';
                            backgroundColor = '#e0360b';
                        } else {
                            size = '60px';
                            pulseSize = '64px';
                            backgroundColor = '#cc2d06';
                        }

                        const clusterDiv = document.createElement("div");

                        // Create wrapper for pulse effect
                        const pulseWrapper = document.createElement("div");
                        pulseWrapper.style.position = "relative";
                        pulseWrapper.style.width = pulseSize;
                        pulseWrapper.style.height = pulseSize;

                        // Create pulse effect
                        const pulseEffect = document.createElement("div");
                        pulseEffect.style.position = "absolute";
                        pulseEffect.style.width = "100%";
                        pulseEffect.style.height = "100%";
                        pulseEffect.style.borderRadius = "50%";
                        pulseEffect.style.backgroundColor = backgroundColor;
                        pulseEffect.style.opacity = "0.2";
                        pulseEffect.style.animation = "pulseAnimation 2s infinite";

                        // Create main container with enhanced styles
                        const container = document.createElement("div");
                        container.style.width = size;
                        container.style.height = size;
                        container.style.backgroundColor = backgroundColor;
                        container.style.borderRadius = "50%";
                        container.style.display = "flex";
                        container.style.alignItems = "center";
                        container.style.justifyContent = "center";
                        container.style.color = "white";
                        container.style.fontWeight = "600";
                        container.style.fontSize = count > 99 ? "14px" : "16px";
                        container.style.boxShadow = "0 3px 6px rgba(0,0,0,0.3)";
                        container.style.border = "2px solid rgba(255,255,255,0.6)";
                        container.style.position = "absolute";
                        container.style.top = "50%";
                        container.style.left = "50%";
                        container.style.transform = "translate(-50%, -50%)";
                        container.style.transformOrigin = "center";
                        container.style.animation = "clusterAnimation 0.3s ease-in-out";
                        container.style.cursor = "pointer";
                        container.style.transition = "transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out";

                        // Add hover effect
                        container.onmouseenter = () => {
                            container.style.transform = "translate(-50%, -50%) scale(1.1)";
                            container.style.boxShadow = "0 5px 12px rgba(0,0,0,0.4)";
                        };
                        container.onmouseleave = () => {
                            container.style.transform = "translate(-50%, -50%) scale(1)";
                            container.style.boxShadow = "0 3px 6px rgba(0,0,0,0.3)";
                        };

                        // Create count span with enhanced styles
                        const countSpan = document.createElement("span");
                        countSpan.textContent = count > 99 ? "99+" : count;
                        countSpan.style.position = "relative";
                        countSpan.style.zIndex = "2";
                        countSpan.style.textShadow = "1px 1px 2px rgba(0,0,0,0.3)";

                        // Create gradient overlay with enhanced effect
                        const gradientOverlay = document.createElement("div");
                        gradientOverlay.style.position = "absolute";
                        gradientOverlay.style.width = "100%";
                        gradientOverlay.style.height = "100%";
                        gradientOverlay.style.borderRadius = "50%";
                        gradientOverlay.style.background =
                            "radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%)";
                        gradientOverlay.style.pointerEvents = "none";

                        // Create and add enhanced animations
                        const style = document.createElement("style");
                        style.textContent = `@keyframes clusterAnimation {
        from {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 0;
        }
        to {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }
    }
    @keyframes pulseAnimation {
        0% {
            transform: scale(0.95);
            opacity: 0.5;
        }
        50% {
            transform: scale(1.05);
            opacity: 0.2;
        }
        100% {
            transform: scale(0.95);
            opacity: 0.5;
        }
    }
`;

                        // Only add style if it doesn't exist
                        if (!document.getElementById('cluster-animations')) {
                            style.id = 'cluster-animations';
                            document.head.appendChild(style);
                        }

                        // Assemble the components
                        container.appendChild(countSpan);
                        container.appendChild(gradientOverlay);
                        pulseWrapper.appendChild(pulseEffect);
                        pulseWrapper.appendChild(container);
                        clusterDiv.appendChild(pulseWrapper);

                        return new google.maps.marker.AdvancedMarkerElement({
                            position,
                            content: clusterDiv,
                            zIndex: 1
                        });
                    }
                }
            });
        }


        function updateMarkers(vehicleData, AdvancedMarkerElement) {
            // Clear existing markers
            markers.forEach(marker => marker.map = null);
            markers = [];

            // Loop through vehicles and add markers
            Object.keys(vehicleData).forEach(type => {
                let vehicles = vehicleData[type] || vehicleData['default'];

                // Handle case when vehicles is an object instead of array
                if (vehicles && typeof vehicles === 'object' && !Array.isArray(vehicles)) {
                    // Convert object to array
                    vehicles = Object.values(vehicles);
                }

                // Ensure vehicles is defined and not empty
                if (!vehicles || vehicles.length === 0) return;
                vehicles.forEach(vehicle => {
                    // More precise coordinate parsing
                    const latitude = parseFloat(vehicle.latitude);
                    const longitude = parseFloat(vehicle.longitude);
                    const movementStatus = parseInt(vehicle.movement_status);
                    const ignitionStatus = parseInt(vehicle.ignition_status);
                    const speed = parseInt(vehicle.speed);
                    const angle = parseFloat(vehicle.angle) || 0;

                    // Validate coordinates
                    if (!isValidCoordinate(latitude, longitude)) return;

                    // Determine icon color based on vehicle status
                    let iconColor = determineVehicleStatus(movementStatus, ignitionStatus, speed);

                    // Create custom marker content with improved positioning
                    const markerDiv = document.createElement('div');
                    markerDiv.innerHTML = `
                <div style="position:relative; transform-origin: center bottom;">
                    <img src="{{ asset('assets/images/icons/${vehicle.icon}/${iconColor}.png') }}"
                         style="width: auto; height: 60px; transform: rotate(${angle}deg);
                         filter: drop-shadow(2px 4px 6px rgba(0,0,0,0.9));">

                    <!-- Vehicle Info Box -->
                    <div style="position:absolute; left: 50%; transform: translateX(-50%); bottom:-40px;
                                background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(50, 50, 50, 0.6));
                                color: white; padding: 6px 10px; border-radius: 8px; font-size: 10px;
                                font-weight: 600; box-shadow: 2px 4px 8px rgba(0, 0, 0, 0.4);
                                white-space: nowrap;">
                        <div style="display:flex;align-items:center;gap:5px; margin-bottom: 3px;">
                            <img src="{{ asset('assets/images/icons/plate.svg') }}"
                                 style="width: 12px; height: 12px;">
                            <span>${vehicle.license_plate}</span>
                        </div>
                        ${vehicle.driver?.name ? `<div style="display:flex;align-items:center;gap:5px;"><img src="{{ asset('assets/images/icons/driver.svg') }}"style="width: 12px; height: 12px;"><span>${vehicle.driver.name}</span> </div>` : ''}
                    </div>
                </div>
            `;

                    // check if google is initialized
                    if (!google || !google.maps) {
                        return;
                    }

                    // Create marker with improved options
                    const marker = new google.maps.marker.AdvancedMarkerElement({
                        map,
                        position: {
                            lat: latitude,
                            lng: longitude
                        },
                        content: markerDiv,
                        collisionBehavior: google.maps.CollisionBehavior
                            .OPTIONAL_AND_HIDES_LOWER_PRIORITY,
                        zIndex: selectedVehicleImei === vehicle.imei ? 1000 : 1
                    });

                    // Add click event to marker
                    marker.addListener('click', () => {
                        focusOnMarker(vehicle.imei, latitude, longitude, vehicle.icon,
                            iconColor);

                        // window.dispatchEvent(
                        //     new CustomEvent('show-vehicle-details', {
                        //         detail: vehicle,
                        //     })
                        // );

                        @this.call('updateVehicleDetails', vehicle);
                    });


                    // Update selected vehicle position if in live mode
                    let liveMode = document.getElementById('liveModeToggle')?.checked;
                    if (liveMode && selectedVehicleImei === vehicle.imei && iconColor == 'green') {
                        map.panTo({
                            lat: latitude,
                            lng: longitude
                        });
                    }

                    markers.push(marker);
                });
            });

            initializeMarkerCluster();
        }

        // Helper functions
        function isValidCoordinate(lat, lng) {
            return !isNaN(lat) && !isNaN(lng) &&
                lat !== 0 && lng !== 0 &&
                lat >= -90 && lat <= 90 &&
                lng >= -180 && lng <= 180;
        }

        function determineVehicleStatus(movement, ignition, speed) {
            if (movement == 1 && speed > 0) return 'green';
            if (movement == 0 && ignition == 1) return 'yellow';
            return 'red';
        }




        function focusOnMarker(imei, latitude, longitude, icon = null, color = null) {
            selectedVehicleImei = imei;
            seletedVehicleIcon = icon;
            selectedVehicleLat = latitude;
            selectedVehicleLng = longitude;


            // let liveMode = document.getElementById('liveMode')?.checked;
            // if (selectedVehicleImei && liveMode == false) {
            //     return;
            // }

            // updateMode();

            if (!latitude || !longitude) return;

            const position = {
                lat: parseFloat(latitude),
                lng: parseFloat(longitude)
            };

            // Pan and zoom to the position
            map.panTo(position);
            map.setZoom(18);


            if (latitude && longitude) {
                // Get address
                getAddressFromCoordinates(latitude, longitude).then(address => {
                    if (document.getElementById('address')) {
                        document.getElementById('address').innerHTML = address || "Address not found";
                    }
                });

                const position = {
                    lat: parseFloat(latitude),
                    lng: parseFloat(longitude)
                };

                // Add Street View
                const streetViewService = new google.maps.StreetViewService();

                streetViewService.getPanorama({
                    location: position,
                    radius: 50,
                    source: google.maps.StreetViewSource.OUTDOOR
                }, (data, status) => {
                    if (status === google.maps.StreetViewStatus.OK) {
                        // Create or get street view container
                        let streetViewDiv = document.getElementById('street-view');
                        if (!streetViewDiv) {
                            streetViewDiv = document.createElement('div');
                            streetViewDiv.id = 'street-view';
                            streetViewDiv.style.width = '100%';
                            streetViewDiv.style.height = '200px';
                            streetViewDiv.style.marginTop = '10px';
                            streetViewDiv.style.borderRadius = '8px';
                            streetViewDiv.style.overflow = 'hidden';

                            // Insert after address element
                            const addressElement = document.getElementById('address');
                            if (addressElement && addressElement.parentNode) {
                                addressElement.parentNode.insertBefore(streetViewDiv, addressElement.nextSibling);
                            }
                        }

                        // Initialize Street View Panorama
                        const panorama = new google.maps.StreetViewPanorama(streetViewDiv, {
                            position: position,
                            pov: {
                                heading: 34,
                                pitch: 10
                            },
                            addressControl: true,
                            fullscreenControl: true,
                            linksControl: false,
                            panControl: false,
                            enableCloseButton: false,
                            zoomControl: true,
                            visible: true
                        });

                        // Connect the panorama to the map
                        map.setStreetView(panorama);

                    } else {
                        // Remove street view if not available
                        const streetViewDiv = document.getElementById('street-view');
                        if (streetViewDiv) {
                            streetViewDiv.remove();
                        }
                    }
                });
            }



        }


        function initializeRoute(current_route) {
            // Clear previous routes
            allRoutes.forEach(route => route.setMap(null));
            allRoutes = [];
            allMarkers.forEach(marker => marker.setMap(null));
            allMarkers = [];

            if (current_route) {
                const {
                    start_point_lat,
                    start_point_lng,
                    end_point_lat,
                    end_point_lng,
                    stops,
                    route,
                    start_point_status,
                    end_point_status,
                    start_point_completed_at,
                    end_point_completed_at,
                    start_point_odometer,
                    end_point_odometer,
                    start_point_fuel,
                    end_point_fuel
                } = current_route;

                if (!start_point_lat || !start_point_lng || !end_point_lat || !end_point_lng || !route) return;

                // Parse the route
                let routePath = google.maps.geometry.encoding.decodePath(route);

                // Create segments based on stops
                let allPoints = [{
                    lat: parseFloat(start_point_lat),
                    lng: parseFloat(start_point_lng),
                    status: start_point_status
                }];

                // Add stops in order
                if (stops && stops.length > 0) {
                    stops.forEach(stop => {
                        allPoints.push({
                            lat: parseFloat(stop.latitude),
                            lng: parseFloat(stop.longitude),
                            status: stop.status
                        });
                    });
                }

                // Add end point
                allPoints.push({
                    lat: parseFloat(end_point_lat),
                    lng: parseFloat(end_point_lng),
                    status: end_point_status
                });

                // Create route segments between points
                for (let i = 0; i < allPoints.length - 1; i++) {
                    const startPoint = allPoints[i];
                    const endPoint = allPoints[i + 1];

                    // Find route segment between these points
                    const segmentPath = findRouteSegment(routePath, startPoint, endPoint);

                    // Create polyline for this segment
                    const segmentPolyline = new google.maps.Polyline({
                        path: segmentPath,
                        geodesic: true,
                        strokeColor: (startPoint.status === 'completed' && endPoint.status === 'completed') ?
                            '#10B981' : '#2563EB', // Green only if both points are completed
                        strokeOpacity: 1.0,
                        strokeWeight: 4,
                        map: map,
                        icons: [{
                            icon: {
                                path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                                scale: 3,
                                strokeColor: '#FFFFFF',
                                strokeWeight: 2,
                                fillColor: (startPoint.status === 'completed' && endPoint.status ===
                                        'completed') ?
                                    '#10B981' : '#2563EB',
                                fillOpacity: 1
                            },
                            offset: '50px',
                            repeat: '100px'
                        }]
                    });
                    allRoutes.push(segmentPolyline);
                }

                // Add markers (rest of your existing marker code)
                startRoute = addCustomMarker({
                    lat: parseFloat(start_point_lat),
                    lng: parseFloat(start_point_lng)
                }, 'start', start_point_status, {
                    start_point_completed_at,
                    start_point_odometer,
                    start_point_fuel
                });

                // For stops, we need to track the previous point
                let previousPoint = {
                    odometer: start_point_odometer,
                    fuel: start_point_fuel
                };

                stopsRoute = stops?.map(stop => {
                    const marker = addCustomMarker({
                            lat: parseFloat(stop.latitude),
                            lng: parseFloat(stop.longitude)
                        },
                        'stop',
                        stop.status, {
                            previousPoint,
                            completed_at: stop.completed_at,
                            stop_odometer: stop.stop_odometer,
                            stop_fuel: stop.stop_fuel
                        }
                    );

                    // Update previous point for next iteration
                    previousPoint = {
                        odometer: stop.stop_odometer,
                        fuel: stop.stop_fuel
                    };

                    return marker;
                }) || [];

                endRoute = addCustomMarker({
                        lat: parseFloat(end_point_lat),
                        lng: parseFloat(end_point_lng)
                    },
                    'end',
                    end_point_status, {
                        previousPoint,
                        end_point_completed_at,
                        end_point_odometer,
                        end_point_fuel,
                        start_point_odometer,
                        start_point_fuel
                    }
                );

                // Fit bounds
                const bounds = new google.maps.LatLngBounds();
                routePath.forEach(point => bounds.extend(point));
                map.fitBounds(bounds);
            }
        }

        // Custom marker creation function with improved styling
        function addCustomMarker(position, type, status, pointData) {
            // Create main container
            const markerContainer = document.createElement('div');
            markerContainer.className = 'marker-container';
            markerContainer.style.position = 'relative';

            // Create marker icon container
            const markerIcon = document.createElement('div');
            markerIcon.className = 'marker-icon';
            const statusColor = status === 'completed' ? '#10B981' : '#EF4444';
            Object.assign(markerIcon.style, {
                width: '40px',
                height: '40px',
                background: statusColor,
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                border: '2px solid white'
            });

            // Create icon image
            const iconImg = document.createElement('img');
            iconImg.src = `{{ asset('assets/images/icons/${type}.svg') }}`;
            Object.assign(iconImg.style, {
                width: '24px',
                height: '24px',
                filter: 'brightness(0) invert(1)'
            });
            markerIcon.appendChild(iconImg);

            // Create pulse effect
            const pulseEffect = document.createElement('div');
            pulseEffect.className = 'pulse-effect';
            Object.assign(pulseEffect.style, {
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '40px',
                height: '40px',
                background: statusColor,
                borderRadius: '50%',
                opacity: '0.5',
                animation: 'pulse 1.5s infinite'
            });

            // Assemble the marker
            markerContainer.appendChild(markerIcon);
            if (status != 'completed') markerContainer.appendChild(pulseEffect);

            // Create the marker
            const marker = new google.maps.marker.AdvancedMarkerElement({
                position,
                content: markerContainer,
                map,
                zIndex: 2
            });

            // Create info window content based on type and data
            let infoContent = null;
            if (pointData && type === 'start' && status == 'completed') {
                infoContent = `
    <div style="padding: 15px; min-width: 300px;">
        <div style="display: flex; align-items: center; margin-bottom: 12px; border-bottom: 2px solid #E5E7EB; padding-bottom: 12px;">
            <div style="width: 40px; height: 40px; background: #10B981; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 2px solid white;margin-right: 10px;">
             <img src="{{ asset('assets/images/icons/start.svg') }}" style="width: 24px; height: 24px; ">
            </div>
            <h3 style="margin: 0; color: #1F2937; font-size: 16px; font-weight: 600;">@lang('translations.start_point')</h3>
        </div>
        <div style="color: #4B5563; font-size: 14px;">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <div style="width: 8px; height: 8px; border-radius: 50%; background-color: ${status === 'completed' ? '#10B981' : '#EF4444'}; margin-right: 8px;"></div>
                <span>@lang('translations.status') ${status.charAt(0).toUpperCase() + status.slice(1)}</span>
            </div>
            ${pointData.start_point_completed_at ? `<div style="display: flex; align-items: center; margin-bottom: 8px;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <img src="{{ asset('assets/images/icons/clock.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <span>@lang('translations.completed_at'): ${formatDateTime(pointData.start_point_completed_at)}</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </div>` : ''}
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/odometer.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.initial_odometer'): ${formatNumber(pointData.start_point_odometer)} km</span>
            </div>
            <div style="display: flex; align-items: center;">
                <img src="{{ asset('assets/images/icons/fuel-used.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.initial_fuel'): ${pointData.start_point_fuel || 'N/A'} L</span>
            </div>
        </div>
    </div>`;
            } else if (pointData && type === 'stop' && status == 'completed') {
                const prevPoint = pointData.previousPoint || {};
                const distance = calculateDistance(prevPoint.odometer, pointData.stop_odometer);
                const fuelConsumption = calculateFuelConsumption(prevPoint.fuel, pointData.stop_fuel);

                infoContent = `
    <div style="padding: 15px; min-width: 300px;">
        <div style="display: flex; align-items: center; margin-bottom: 12px; border-bottom: 2px solid #E5E7EB; padding-bottom: 12px;">
            <div style="width: 40px; height: 40px; background: #10B981; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 2px solid white;margin-right: 10px;">
            <img src="{{ asset('assets/images/icons/stop.svg') }}" style="width: 24px; height: 24px; ">
            </div>
            <h3 style="margin: 0; color: #1F2937; font-size: 16px; font-weight: 600;">@lang('translations.stop_point')</h3>
        </div>
        <div style="color: #4B5563; font-size: 14px;">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <div style="width: 8px; height: 8px; border-radius: 50%; background-color: ${status === 'completed' ? '#10B981' : '#EF4444'}; margin-right: 8px;"></div>
                <span>@lang('translations.status'): ${status.charAt(0).toUpperCase() + status.slice(1)}</span>
            </div>
            ${pointData.completed_at ? `<div style="display: flex; align-items: center; margin-bottom: 8px;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <img src="{{ asset('assets/images/icons/clock.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <span>@lang('translations.completed_at'): ${formatDateTime(pointData.completed_at)}</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </div>` : ''}
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/distance.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.distance_from_previous'): ${distance} km</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/fuel-consumption-icon.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.fuel_consumption'): ${fuelConsumption} L</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/odometer.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.current_odometer'): ${formatNumber(pointData.stop_odometer)} km</span>
            </div>
            <div style="display: flex; align-items: center;">
                <img src="{{ asset('assets/images/icons/fuel-used.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.current_fuel'): ${pointData.stop_fuel || 'N/A'} L</span>
            </div>
        </div>
    </div>`;
            } else if (pointData && type === 'end' && status == 'completed') {
                const prevPoint = pointData.previousPoint || {};
                const distance = calculateDistance(prevPoint.odometer, pointData.end_point_odometer);
                const fuelConsumption = calculateFuelConsumption(prevPoint.fuel, pointData.end_point_fuel);
                const totalDistance = calculateDistance(pointData.start_point_odometer, pointData.end_point_odometer);
                const totalFuel = calculateFuelConsumption(pointData.start_point_fuel, pointData.end_point_fuel);

                infoContent = `
    <div style="padding: 15px; min-width: 300px;">
        <div style="display: flex; align-items: center; margin-bottom: 12px; border-bottom: 2px solid #E5E7EB; padding-bottom: 12px;">
               <div style="width: 40px; height: 40px; background: #10B981; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 2px solid white;margin-right: 10px;">
            <img src="{{ asset('assets/images/icons/end.svg') }}" style="width: 24px; height: 24px; ">
            </div>
            <h3 style="margin: 0; color: #1F2937; font-size: 16px; font-weight: 600;">@lang('translations.end_point')</h3>
        </div>
        <div style="color: #4B5563; font-size: 14px;">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <div style="width: 8px; height: 8px; border-radius: 50%; background-color: ${status === 'completed' ? '#10B981' : '#EF4444'}; margin-right: 8px;"></div>
                <span>@lang('translations.status'): ${status.charAt(0).toUpperCase() + status.slice(1)}</span>
            </div>
            ${pointData.end_point_completed_at ? ` <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <img src="{{ asset('assets/images/icons/clock.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <span>@lang('translations.completed_at'): ${formatDateTime(pointData.end_point_completed_at)}</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </div>` : ''}
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/distance.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.distance_from_previous'): ${distance} km</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/fuel-consumption-icon.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.fuel_consumption'): ${fuelConsumption} L</span>
            </div>
            <div style="margin: 12px 0; border-top: 1px solid #E5E7EB; padding-top: 12px;">
                <div style="font-weight: 600; color: #1F2937; margin-bottom: 8px; display: flex; align-items: center;">
                    <img src="{{ asset('assets/images/icons/stats.svg') }}" style="width: 16px; height: 16px; margin-right: 8px;">
                    @lang('translations.total_route_statistics')
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <img src="{{ asset('assets/images/icons/distance.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.total_distance'): ${totalDistance} km</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <img src="{{ asset('assets/images/icons/fuel-consumption-icon.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.total_fuel_used'): ${totalFuel} L</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <img src="{{ asset('assets/images/icons/odometer.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.final_odometer'): ${formatNumber(pointData.end_point_odometer)} km</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <img src="{{ asset('assets/images/icons/fuel-used.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.final_fuel'): ${pointData.end_point_fuel || 'N/A'} L</span>
                </div>
            </div>
        </div>
    </div>`;
            }

            if (infoContent) {
                // Create and attach info window
                const infoWindow = new google.maps.InfoWindow({
                    content: infoContent
                });

                // Add mouseover listener
                marker.addListener("mouseover", () => {
                    infoWindow.open(map, marker);

                });

                // Add mouseout listener
                marker.addListener("mouseout", () => {
                    infowindow.close();
                });
            }



            // Store info window reference
            // allInfoWindows.push(infoWindow);
            allMarkers.push(marker);

            return marker;
        }

        // Helper functions
        function formatDateTime(dateString) {
            return new Date(dateString).toLocaleString();
        }

        function formatNumber(number) {
            return number ? (number / 1000).toFixed(2) : 'N/A';
        }

        function calculateDistance(prevOdometer, currentOdometer) {
            if (!prevOdometer || !currentOdometer) return 'N/A';
            return ((currentOdometer - prevOdometer) / 1000).toFixed(2);
        }

        function calculateFuelConsumption(prevFuel, currentFuel) {
            if (!prevFuel || !currentFuel) return 'N/A';
            return Math.abs(prevFuel - currentFuel).toFixed(2);
        }

        // Helper function to find route segment between two points
        function findRouteSegment(fullPath, startPoint, endPoint) {
            // Find the closest points in the full path to our start and end points
            let startIdx = 0;
            let endIdx = fullPath.length - 1;
            let minStartDist = Infinity;
            let minEndDist = Infinity;

            for (let i = 0; i < fullPath.length; i++) {
                const point = fullPath[i];

                // Calculate distance to start point
                const startDist = getDistance({
                        lat: point.lat(),
                        lng: point.lng()
                    },
                    startPoint
                );

                // Calculate distance to end point
                const endDist = getDistance({
                        lat: point.lat(),
                        lng: point.lng()
                    },
                    endPoint
                );

                if (startDist < minStartDist) {
                    minStartDist = startDist;
                    startIdx = i;
                }

                if (endDist < minEndDist) {
                    minEndDist = endDist;
                    endIdx = i;
                }
            }

            // Return the segment of the path between these points
            return fullPath.slice(startIdx, endIdx + 1);
        }

        // Helper function to calculate distance between two points
        function getDistance(p1, p2) {
            return google.maps.geometry.spherical.computeDistanceBetween(
                new google.maps.LatLng(p1.lat, p1.lng),
                new google.maps.LatLng(p2.lat, p2.lng)
            );
        }





        let historyData = [];
        let historyMarker = null; // Reference for the history marker
        let path = null; // Reference for the Polyline
        let pathCoordinates = [];

        // if (document.getElementById('liveMode')) {
        //     document.getElementById('liveMode').addEventListener('change', updateMode);
        //     const dateDropdown = document.getElementById('dateDropdown');
        // }

        // async function updateMode() {
        //     let liveMode = document.getElementById('liveMode')?.checked;
        //     document.getElementById('historyControls').style.display = liveMode ? 'none' : 'block';

        //     clearMapElements();

        //     if (!liveMode) {
        //         dateDropdown.innerHTML = '';
        //         await fetchAvailableDates();
        //     } else {
        //         historyData = [];


        //         if (selectedVehicleLat && selectedVehicleLng) {
        //             const latitude = parseFloat(selectedVehicleLat);
        //             const longitude = parseFloat(selectedVehicleLng);

        //             if (!latitude || !longitude) return;

        //             const position = {
        //                 lat: parseFloat(latitude),
        //                 lng: parseFloat(longitude)
        //             };

        //             // Pan and zoom to the position
        //             map.panTo(position);
        //             map.setZoom(18);
        //         }
        //     }
        // }

        // async function fetchAvailableDates() {
        //     try {
        //         const response = await fetch(
        //             `{{ url('/') }}/data/history/${selectedVehicleImei}/dates.json?nocache=${Date.now()}`);


        //         if (!response.ok) {
        //             // If file doesn't exist (404), simply return without breaking the script
        //             if (response.status === 404) {
        //                 console.warn("No available dates found.");
        //                 return;
        //             }
        //             throw new Error(`HTTP error! Status: ${response.status}`);
        //         }

        //         const dates = await response.json();

        //         if (dates.length > 0) {
        //             populateDateDropdown(dates);
        //             fetchHistoryData(dates[0]); // Fetch data for the first date
        //         }
        //     } catch (error) {
        //         console.error("Error fetching available dates:", error);
        //     }
        // }


        // function populateDateDropdown(dates) {
        //     dateDropdown.innerHTML = ""; // Clear existing options

        //     dates.forEach((date, index) => {
        //         const option = document.createElement("option");
        //         option.value = date;
        //         option.textContent = date;
        //         if (index === 0) option.selected = true;
        //         dateDropdown.appendChild(option);
        //     });

        //     dateDropdown.addEventListener("change", (event) => {
        //         fetchHistoryData(event.target.value);
        //     });
        // }

        // async function fetchHistoryData(selectedDate) {
        //     try {
        //         clearMapElements();
        //         historyData = [];

        //         const response = await fetch(
        //             `{{ asset('data/history/${selectedVehicleImei}/${selectedDate}.json') }}`);

        //         if (!response.ok) {
        //             // If file doesn't exist (404), log a warning and exit gracefully
        //             if (response.status === 404) {
        //                 console.warn(`History data for ${selectedDate} not found.`);
        //                 return;
        //             }
        //             throw new Error(`HTTP error! Status: ${response.status}`);
        //         }

        //         historyData = await response.json();

        //         plotPathOnMap();
        //         initializeHistoryMarker(seletedVehicleIcon, 'red');
        //     } catch (error) {
        //         console.error("Error fetching history data:", error);
        //     }
        // }



        let startMarker = null;
        let endMarker = null;
        let infoWindows = [];
        let infoMarkers = [];
        let historyPath = null;
        let stopMarkers = [];

        function plotPathOnMap() {
            if (!historyData || historyData.length === 0) return;

            // Clear existing path coordinates
            pathCoordinates = [];
            let stopPoints = [];
            const DISTANCE_THRESHOLD = 50; // meters
            const SPEED_THRESHOLD = 15; // km/h
            const ANGLE_THRESHOLD = 45; // degrees

            let lastAddedPoint = null;
            let currentStopStart = null;

            let lastTripStop = null;
            let nextTripStart = null;

            let totalOdometer = 0; // Track cumulative distance

            // Process history data
            historyData.forEach((item, index) => {
                if (!item.latitude || !item.longitude) return;

                const point = {
                    lat: parseFloat(item.latitude),
                    lng: parseFloat(item.longitude),
                    timestamp: item?.last_update,
                    angle: parseFloat(item?.angle) || 0,
                    speed: parseFloat(item?.speed) || 0,
                    ignition: item["239"], // ignition status
                    movement: item["240"], // movement status
                    trip_event: item.eventID,
                    trip_status: parseInt(item["250"]) || 0, // trip status
                    trip_odometer: item["199"] || 0, // trip odometer
                    index: index
                };

                // Update total odometer from start
                if (index === 0) {
                    totalOdometer = point.trip_odometer;
                }


                // Always add first and last points
                if (index === 0 || index === historyData.length - 1) {
                    pathCoordinates.push(point);
                    lastAddedPoint = point;
                    return;
                }

                // Detect stops (vehicle not moving with ignition on)
                if (point.speed < 1 && point.ignition === 1) {
                    if (!currentStopStart) {
                        currentStopStart = point;
                        stopPoints.push(point);
                    }
                } else {
                    currentStopStart = null;
                }

                // Track trip events for stop duration
                if (point.trip_event === 250) {
                    if (point.trip_status === 0) { // Vehicle stopped
                        if (!lastTripStop) { // Only set if not already tracking a stop
                            lastTripStop = point;
                            lastTripStop.totalOdometer = point.trip_odometer; // Store total distance at stop
                        }
                    } else if (point.trip_status === 1) { // Vehicle started
                        if (lastTripStop) {
                            // Calculate stop duration
                            const stopDuration = calculateDuration(lastTripStop.timestamp, point.timestamp);
                            stopPoints.push({
                                ...lastTripStop,
                                duration: stopDuration,
                                startTime: lastTripStop.timestamp,
                                endTime: point.timestamp,
                                trip_odometer: point.trip_odometer - lastTripStop.trip_odometer,
                                total_distance: (lastTripStop.totalOdometer / 1000).toFixed(
                                    2) // Convert to km
                            });
                        }
                        lastTripStop = null;
                    }
                } else if (point.speed < 1 && point.ignition === 1) {
                    // Backup method: Also detect stops based on speed and ignition
                    if (!lastTripStop) {
                        lastTripStop = point;
                        lastTripStop.totalOdometer = point.trip_odometer;
                    }
                } else if (point.speed > 5 && lastTripStop) {
                    // Vehicle started moving again
                    const stopDuration = calculateDuration(lastTripStop.timestamp, point.timestamp);
                    stopPoints.push({
                        ...lastTripStop,
                        duration: stopDuration,
                        startTime: lastTripStop.timestamp,
                        endTime: point.timestamp,
                        trip_odometer: point.trip_odometer - lastTripStop.trip_odometer,
                        total_distance: (lastTripStop.totalOdometer / 1000).toFixed(2) // Convert to km
                    });
                    lastTripStop = null;
                }

                // Add points based on significant changes
                if (lastAddedPoint) {
                    // Use Haversine formula for distance calculation
                    const calculateDistance = (lat1, lon1, lat2, lon2) => {
                        const R = 6371000;
                        const φ1 = lat1 * Math.PI / 180;
                        const φ2 = lat2 * Math.PI / 180;
                        const Δφ = (lat2 - lat1) * Math.PI / 180;
                        const Δλ = (lon2 - lon1) * Math.PI / 180;

                        const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
                            Math.cos(φ1) * Math.cos(φ2) *
                            Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
                        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

                        return R * c;
                    };

                    const distance = calculateDistance(
                        lastAddedPoint.lat, lastAddedPoint.lng,
                        point.lat, point.lng
                    );

                    const speedChange = Math.abs(point.speed - lastAddedPoint.speed);
                    const angleChange = Math.abs(point.angle - lastAddedPoint.angle);

                    if (distance > DISTANCE_THRESHOLD ||
                        speedChange > SPEED_THRESHOLD ||
                        angleChange > ANGLE_THRESHOLD) {
                        pathCoordinates.push(point);
                        lastAddedPoint = point;
                    }
                }
            });

            // Draw the path
            historyPath = new google.maps.Polyline({
                path: pathCoordinates,
                geodesic: true,
                strokeColor: '#F54619',
                strokeOpacity: 0.8,
                strokeWeight: 3,
                icons: [{
                    icon: {
                        path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                        scale: 5,
                        fillColor: '#FFFFFF',
                        fillOpacity: 1,
                        strokeColor: '#F54619',
                        strokeWeight: 2
                    },
                    repeat: '150px'
                }],
                map: map
            });

            // Add mouseover and mouseout listeners to the polyline
            let hoverInfoWindow = null;

            google.maps.event.addListener(historyPath, 'mouseover', function(e) {
                // Find the closest point to where the user hovered
                const closestPoint = pathCoordinates.reduce((prev, curr) => {
                    const prevDistance = google.maps.geometry.spherical.computeDistanceBetween(
                        new google.maps.LatLng(prev.lat, prev.lng),
                        e.latLng
                    );
                    const currDistance = google.maps.geometry.spherical.computeDistanceBetween(
                        new google.maps.LatLng(curr.lat, curr.lng),
                        e.latLng
                    );
                    return prevDistance < currDistance ? prev : curr;
                });

                const content = `
        <div class="relative min-w-[250px] bg-white rounded-lg shadow-lg border border-gray-100">
            <!-- Main Content -->
            <div class="p-4">
                <!-- Speed Section -->
                <div class="flex items-center justify-between pb-3 mb-3 border-b border-gray-100">
                    <div class="flex items-center gap-3">
                        <div class="p-2 rounded-full bg-primary/10">
                            <svg class="w-5 h-5 text-primary" fill="currentColor" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                <path d="M10,20C4.5,20,0,15.5,0,10S4.5,0,10,0s10,4.5,10,10S15.5,20,10,20z M10,2c-4.4,0-8,3.6-8,8s3.6,8,8,8s8-3.6,8-8S14.4,2,10,2z"></path>
                                <path d="M8.6,11.4c-0.8-0.8-2.8-5.7-2.8-5.7s4.9,2,5.7,2.8c0.8,0.8,0.8,2,0,2.8C10.6,12.2,9.4,12.2,8.6,11.4z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-sm text-gray-500">@lang('translations.speed')</div>
                            <div class="text-lg font-semibold text-gray-800">${closestPoint.speed} km/h</div>
                        </div>
                    </div>
                </div>

                <!-- Time Section -->
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-gray-100 rounded-full">
                        <svg class="w-5 h-5 text-gray-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12ZM3.00683 12C3.00683 16.9668 7.03321 20.9932 12 20.9932C16.9668 20.9932 20.9932 16.9668 20.9932 12C20.9932 7.03321 16.9668 3.00683 12 3.00683C7.03321 3.00683 3.00683 7.03321 3.00683 12Z" fill="currentColor"/>
                            <path d="M12 5C11.4477 5 11 5.44771 11 6V12.4667C11 12.4667 11 12.7274 11.1267 12.9235C11.2115 13.0898 11.3437 13.2343 11.5174 13.3346L16.1372 16.0019C16.6155 16.278 17.2271 16.1141 17.5032 15.6358C17.7793 15.1575 17.6155 14.5459 17.1372 14.2698L13 11.8812V6C13 5.44772 12.5523 5 12 5Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">@lang('translations.time')</div>
                        <div class="text-sm font-medium text-gray-700">${formatTimestamp(closestPoint.timestamp)}</div>
                    </div>
                </div>
            </div>

            <!-- Speech Bubble Arrow -->
            <div class="absolute w-4 h-4 rotate-45 -translate-x-1/2 bg-white border-b border-r border-gray-100 -bottom-2 left-1/2"></div>
        </div>
    `;

                // Close previous hover info window if exists
                if (hoverInfoWindow) {
                    hoverInfoWindow.close();
                }

                // Create and open new info window
                hoverInfoWindow = new google.maps.InfoWindow({
                    content,
                    position: e.latLng,
                    pixelOffset: new google.maps.Size(0, -20),
                    disableAutoPan: true,
                });

                // Remove default InfoWindow styles
                google.maps.event.addListenerOnce(hoverInfoWindow, 'domready', function() {
                    const bubbleContainer = document.querySelector('.gm-style-iw-c');
                    const bubbleBackground = document.querySelector('.gm-style-iw-d');
                    const closeButton = document.querySelector('.gm-style-iw-t button');

                    if (bubbleContainer) {
                        bubbleContainer.style.padding = '0';
                        bubbleContainer.style.borderRadius = '8px';
                        bubbleContainer.style.boxShadow = '0 4px 6px -1px rgb(0 0 0 / 0.1)';
                    }
                    if (bubbleBackground) {
                        bubbleBackground.style.overflow = 'visible';
                    }
                    if (closeButton) {
                        closeButton.style.display = 'none';
                    }
                });

                hoverInfoWindow.open(map);
            });

            // Close info window when mouse leaves the polyline
            google.maps.event.addListener(historyPath, 'mouseout', function() {
                if (hoverInfoWindow) {
                    hoverInfoWindow.close();
                    hoverInfoWindow = null;
                }
            });

            // Add start marker
            if (pathCoordinates[0]) {
                startMarker = new google.maps.Marker({
                    position: pathCoordinates[0],
                    map: map,
                    icon: {
                        url: "{{ asset('assets/images/icons/start-green.svg') }}",
                        scaledSize: new google.maps.Size(30, 30),
                    }
                });
                createInfoWindow(startMarker, pathCoordinates[0], 'Trip Start');
            }

            // Add stop points with duration information
            stopPoints.forEach(point => {
                // Skip stops with duration less than 1 minute
                if (!point.duration || point.duration === 'N/A') return;

                // Parse duration string (e.g., "2h 30m" or "45m")
                const durationInMinutes = (() => {
                    const hours = point.duration.match(/(\d+)h/);
                    const minutes = point.duration.match(/(\d+)m/);
                    return (hours ? parseInt(hours[1]) * 60 : 0) + (minutes ? parseInt(minutes[1]) : 0);
                })();

                // Only show stops longer than 1 minute
                if (durationInMinutes <= 1) return;

                // Choose icon based on duration
                const iconUrl = durationInMinutes >= 30 ?
                    "{{ asset('assets/images/icons/stop-marker-black.svg') }}" // Black dot for stops >= 30 minutes
                    :
                    "{{ asset('assets/images/icons/key.svg') }}"; // Key icon for short stops

                const iconSize = durationInMinutes >= 30 ? 45 : 35; // Larger size for longer stops

                const marker = new google.maps.Marker({
                    position: point,
                    map: map,
                    icon: {
                        url: iconUrl,
                        scaledSize: new google.maps.Size(iconSize, iconSize),
                    }
                });
                stopMarkers.push(marker);

                // Create info window with stop duration
                const content = `<div style="min-width: 250px; padding: 15px;">
        <div style="display: flex; align-items: center; margin-bottom: 12px; border-bottom: 2px solid #E5E7EB; padding-bottom: 12px;">
            <div style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-right: 10px;">
                <img src="${iconUrl}" style="width: 28px; height: 28px;">
            </div>
            <h3 style="margin: 0; color: #1F2937; font-size: 16px; font-weight: 600;">
                ${durationInMinutes >= 30 ? '@lang('translations.long_stop')' : '@lang('translations.short_stop')'}
            </h3>
        </div>
        <div style="color: #4B5563; font-size: 14px;">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/clock.svg') }}"
                     style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.duration') <strong>${point.duration}</strong></span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/distance.svg') }}"
                     style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <div style="display: flex; flex-direction: column;">
                    <span style="margin-bottom: 4px;">Start: <strong>${formatTimestamp(point.startTime)}</strong></span>
                    <span>@lang('translations.end') <strong>${formatTimestamp(point.endTime)}</strong></span>
                </div>
            </div>
            <div style="display: flex; align-items: center; margin-top: 8px; padding-top: 8px; border-top: 1px solid #E5E7EB;">
                <img src="{{ asset('assets/images/icons/odometer.svg') }}"
                     style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.distance') <strong>${point.total_distance} km</strong></span>
            </div>
        </div>
    </div>
`;

                const infowindow = new google.maps.InfoWindow({
                    content,
                    maxWidth: 300,
                    pixelOffset: new google.maps.Size(0, -20)
                });

                marker.addListener("click", () => {
                    infoWindows.forEach(window => window.close());
                    infowindow.open(map, marker);
                });

                infoWindows.push(infowindow);
            });

            // Add end marker
            if (pathCoordinates[pathCoordinates.length - 1]) {
                endMarker = new google.maps.Marker({
                    position: pathCoordinates[pathCoordinates.length - 1],
                    map: map,
                    icon: {
                        url: "{{ asset('assets/images/icons/end-green.svg') }}",
                        scaledSize: new google.maps.Size(30, 30),
                    }
                });
                createInfoWindow(endMarker, pathCoordinates[pathCoordinates.length - 1], 'Trip End');
            }



            // Fit bounds to show all markers
            const bounds = new google.maps.LatLngBounds();
            pathCoordinates.forEach(point => bounds.extend(point));
            map.fitBounds(bounds);
        }

        // Helper function to format timestamp
        function formatTimestamp(timestamp) {
            if (!timestamp) return 'N/A';

            // Split the date and time
            const [date, time] = timestamp.split(' ');
            // Split date components (assuming DD/MM/YYYY format)
            const [day, month, year] = date.split('/');

            // Create date object (month is 0-based, so subtract 1)
            const dateObj = new Date(year, month - 1, day,
                ...(time ? time.split(':') : []));

            return dateObj.toLocaleString();
        }

        // Helper function to calculate duration between two timestamps
        function calculateDuration(startTime, endTime) {
            if (!startTime || !endTime) return 'N/A';

            // Parse DD/MM/YYYY HH:mm format
            const parseCustomDate = (dateStr) => {
                const [date, time] = dateStr.split(' ');
                const [day, month, year] = date.split('/');
                const [hours, minutes] = time ? time.split(':') : [0, 0];
                return new Date(year, month - 1, day, hours, minutes);
            };

            const start = parseCustomDate(startTime);
            const end = parseCustomDate(endTime);
            const diffMs = end - start;

            const hours = Math.floor(diffMs / (1000 * 60 * 60));
            const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

            let duration = '';
            if (hours > 0) duration += `${hours}h `;
            duration += `${minutes}m`;


            return duration;
        }

        function createInfoWindow(marker, point, type = '') {
            const typeIcons = {
                'Trip Start': 'start-green.svg',
                'Trip End': 'end-green.svg'
            };

            const content = `
        <div style="min-width: 250px; padding: 15px;">
            <div style="display: flex; align-items: center; margin-bottom: 12px; border-bottom: 2px solid #E5E7EB; padding-bottom: 12px;">
                ${type ? `<img src="{{ asset('assets/images/icons/${typeIcons[type]}') }}" style="width: 24px; height: 24px; margin-right: 10px;"><h3 style="margin: 0; color: #1F2937; font-size: 16px; font-weight: 600;">@lang('translations.stop_point')</h3>` : ''}
            </div>
            <div style="color: #4B5563; font-size: 14px;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <img src="{{ asset('assets/images/icons/odometer.svg') }}"
                         style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.speed') <strong>${point.speed} km/h</strong></span>
                </div>
                <div style="display: flex; align-items: center;">
                    <img src="{{ asset('assets/images/icons/clock.svg') }}"
                         style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.time') <strong>${formatTimestamp(point.timestamp)}</strong></span>
                </div>
                ${point.address ? `<div style="display: flex; align-items: center; margin-top: 8px; padding-top: 8px; border-top: 1px solid #E5E7EB;"><img src="{{ asset('assets/images/icons/pin.svg') }}"  style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;"><span style="font-size: 12px;">${point.address}</span></div>                                                                                         ` : ''}
            </div>
        </div>
    `;

            const infowindow = new google.maps.InfoWindow({
                content,
                maxWidth: 300,
                pixelOffset: new google.maps.Size(0, -20)
            });


            marker.addListener("click", () => {
                // Close all other info windows first
                infoWindows.forEach(window => window.close());
                infowindow.open(map, marker);
            });

            infoWindows.push(infowindow);
        }




        // Define arrow symbol that will be placed along the polyline
        function createArrowSymbol(angle) {
            return {
                path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                strokeColor: "#ff0000",
                strokeWeight: 2,
                scale: 3,
                rotation: angle // Rotate arrow based on real vehicle direction
            };
        }





        function clearMapElements() {
            // Clear the Polyline
            if (path) {
                path.setMap(null); // Remove Polyline from the map
                path = null;
            }
            // Clear the Polyline
            if (historyPath) {
                historyPath.setMap(null); // Remove Polyline from the map
                historyPath = null;
            }
            // Clear the Polyline
            if (historyData) {
                historyData = [];
            }

            // Clear the marker
            if (historyMarker) {
                historyMarker.setMap(null); // Remove marker from the map
                historyMarker = null;
            }

            // Clear the Start Marker
            if (startMarker) {
                startMarker.setMap(null); // Remove Start Marker from the map
                startMarker = null;
            }

            // Clear the End Marker
            if (endMarker) {
                endMarker.setMap(null); // Remove End Marker from the map
                endMarker = null;
            }

            // Clear all InfoWindows
            infoWindows.forEach(infowindow => infowindow.close());
            infoWindows = [];

            // Clear markers for speed/time
            if (infoMarkers.length > 0) {
                infoMarkers.forEach(marker => marker.setMap(null));
                infoMarkers = [];
            }

            // Clear stop markers
            if (stopMarkers.length > 0) {
                stopMarkers.forEach(marker => marker.setMap(null));
                stopMarkers = [];
            }

            // Clear path coordinates
            pathCoordinates = [];

            // Clear existing geofences
            drawnGeofences.forEach(function(geofence) {
                geofence.setMap(null);
            });
            drawnGeofences = [];

            // if (startRoute) {
            //     startRoute.map = null;
            //     startRoute = null;
            // }
            // if (endRoute) {
            //     endRoute.map = null;
            //     endRoute = null;
            // }
            // stopsRoute.forEach(marker => {
            //     marker.map = null;
            // });
            // stopsRoute = [];
        }


        function initializeHistoryMarker(icon = null, color = null) {
            let historyIcon = icon ? icon : 'default';
            let historyColor = color ?? 'red';

            if (historyData.length > 0) {
                let lastPoint = historyData[historyData.length - 1];
                let initialPosition = new google.maps.LatLng(
                    parseFloat(lastPoint.latitude),
                    parseFloat(lastPoint.longitude)
                );

                // Create wrapper container
                const wrapperElement = document.createElement('div');
                wrapperElement.className = 'relative flex items-center justify-center';

                // Create outer ring
                const outerRing = document.createElement('div');
                outerRing.className = 'absolute w-[75px] h-[75px] rounded-full';
                outerRing.style.background = 'rgba(245, 69, 25, 0.2)';
                outerRing.style.animation = 'spin 5s linear infinite';

                // Create inner ring
                const innerRing = document.createElement('div');
                innerRing.className = 'absolute w-[65px] h-[65px] rounded-full';
                innerRing.style.border = '2px dashed #F54619';
                innerRing.style.animation = 'spin 5s linear infinite reverse';

                // Main vehicle icon
                const iconImg = document.createElement('img');
                iconImg.src = `{{ asset('assets/images/icons/${historyIcon}/${historyColor}.png') }}`;
                iconImg.className = 'h-[55px] w-auto z-10';
                iconImg.style.transform = `rotate(${lastPoint.angle || 0}deg)`;

                // Add animation styles
                const style = document.createElement('style');
                style.textContent = `
                                @keyframes spin {
                                    from { transform: rotate(0deg); }
                                    to { transform: rotate(360deg); }
                                }
                            `;
                document.head.appendChild(style);

                // Assemble the marker elements
                wrapperElement.appendChild(outerRing);
                wrapperElement.appendChild(innerRing);
                wrapperElement.appendChild(iconImg);

                // Initialize AdvancedMarkerElement
                historyMarker = new google.maps.marker.AdvancedMarkerElement({
                    map: map,
                    position: initialPosition,
                    content: wrapperElement
                });

                // Set initial map view
                map.panTo(initialPosition);
                map.setZoom(16);

                let fullTime = lastPoint?.last_update;
                if (document.getElementById('currentTime')) {
                    document.getElementById('currentTime').innerText = fullTime;
                }
            }
        }



        let currentHistoryIndex = 0;
        let playInterval = null;
        let isPlaying = false;

        function playPause() {
            if (playInterval) {
                clearInterval(playInterval);
                playInterval = null;
                isPlaying = false;
                document.getElementById('playIcon').classList.remove('hidden');
                document.getElementById('pauseIcon').classList.add('hidden');
                document.getElementById('buttonText').textContent = "@lang('translations.play')";
            } else {
                playInterval = setInterval(moveHistoryMarker, 800); // Move every 500ms
                isPlaying = true;
                document.getElementById('playIcon').classList.add('hidden');
                document.getElementById('pauseIcon').classList.remove('hidden');
                document.getElementById('buttonText').textContent = "@lang('translations.pause')";
            }
        }

        // Update marker position based on seekbar change
        function seekToPosition(e) {
            if (document.getElementById('seekbar')) {

                currentHistoryIndex = Math.floor((e / 100) * (historyData.length - 1));
                let point = historyData[currentHistoryIndex];
                if (point?.latitude && point?.longitude) {
                    let markerPosition = new google.maps.LatLng(parseFloat(point.latitude), parseFloat(point
                        .longitude));

                    if (historyMarker) {
                        historyMarker.position = markerPosition; // Update the position directly

                        let ignitionStatus = point["239"]; // 1 for 'On', 0 for 'Off'
                        let movementStatus = point["240"]; // 1 for 'On', 0 for 'Off'
                        let speed = point?.speed; // 1 for 'On', 0 for 'Off'
                        let angle = point.angle;

                        // Determine icon color
                        let iconColor = 'red';
                        if (movementStatus == 1 && speed > 1) {
                            iconColor = 'green';
                        } else if (movementStatus == 0 && ignitionStatus == 1) {
                            iconColor = 'yellow';
                        }



                        // Create wrapper container
                        const wrapperElement = document.createElement('div');
                        wrapperElement.className = 'relative flex items-center justify-center';

                        // Create outer ring
                        const outerRing = document.createElement('div');
                        outerRing.className = 'absolute w-[75px] h-[75px] rounded-full';
                        outerRing.style.background = 'rgba(245, 69, 25, 0.2)';
                        outerRing.style.animation = 'spin 5s linear infinite';

                        // Create inner ring
                        const innerRing = document.createElement('div');
                        innerRing.className = 'absolute w-[65px] h-[65px] rounded-full';
                        innerRing.style.border = '2px dashed #F54619';
                        innerRing.style.animation = 'spin 5s linear infinite reverse';

                        // Main vehicle icon
                        const iconImg = document.createElement('img');
                        iconImg.src =
                            `{{ asset('assets/images/icons/${seletedVehicleIcon}/${iconColor}.png') }}`;
                        iconImg.className = 'h-[55px] w-auto z-10';
                        iconImg.style.transform = `rotate(${angle || 0}deg)`;

                        // Add animation styles
                        const style = document.createElement('style');
                        style.textContent = `
                                @keyframes spin {
                                    from { transform: rotate(0deg); }
                                    to { transform: rotate(360deg); }
                                }
                            `;
                        document.head.appendChild(style);

                        // Assemble the marker elements
                        wrapperElement.appendChild(outerRing);
                        wrapperElement.appendChild(innerRing);
                        wrapperElement.appendChild(iconImg);


                        historyMarker.content = wrapperElement;


                        map.panTo(markerPosition);



                    }
                }

                // Update additional UI elements
                // document.getElementById('speed').innerHTML = `${point?.speed} km/h`;
                document.getElementById('currentTime').innerText = point?.last_update;

                // dispatch event to update speed
                window.dispatchEvent(new CustomEvent('speed-updated', {
                    detail: point?.speed
                }));

                // Your existing fuel level calculation
                let fuelLevel = 0; // default value
                if (point && point['37'] !== undefined && point['37'] !== null) {
                    fuelLevel = point['37'] || 0;
                } else if (point && point['87'] !== undefined && point['87'] !== null) {
                    fuelLevel = point['87'] || 0;
                } else if (point && point['89'] !== undefined && point['89'] !== null) {
                    fuelLevel = point['89'] || 0;
                } else if (point && point['48'] !== undefined && point['48'] !== null) {
                    fuelLevel = point['48'] || 0;
                }

                if (fuelLevel !== 0 && fuelLevel !== null) {
                    window.dispatchEvent(new CustomEvent('fuel-level-updated', {
                        detail: fuelLevel
                    }));
                }
            }

        }

        function getDevicePin(ignitionStatus, movementStatus, speed, callback) {
            // Use the cached vehicle type
            let vehicleType = seletedVehicleIcon || 'default';
            let color = getPinColor(ignitionStatus, movementStatus, speed);
            let iconUrl = `{{ asset('assets/images/icons/${vehicleType}/${color}.png') }}`;
            callback(iconUrl);

        }

        // Function to determine the pin color
        function getPinColor(ignitionStatus, movementStatus, speed = 0) {
            if (movementStatus == 1 && speed > 0) {
                return "green";
            } else if (ignitionStatus == 1 && movementStatus == 0) {
                return "yellow";
            } else {
                if (movementStatus == 1 && ignitionStatus == 1) {
                    return "green";
                } else {
                    return "red";
                }
            }
        }


        // Play/Pause logic and updating the marker
        function moveHistoryMarker() {
            if (currentHistoryIndex < historyData.length - 1) {
                currentHistoryIndex++;
                let point = historyData[currentHistoryIndex];
                if (point.latitude && point.longitude) {
                    let markerPosition = new google.maps.LatLng(parseFloat(point.latitude), parseFloat(point.longitude));

                    // Move the marker
                    if (historyMarker) {
                        historyMarker.position = markerPosition; // Update the position directly
                        let ignitionStatus = point["239"]; // 1 for 'On', 0 for 'Off'
                        let movementStatus = point["240"]; // 1 for 'On', 0 for 'Off'
                        let speed = point?.speed; // 1 for 'On', 0 for 'Off'
                        let angle = point?.angle;

                        // Determine icon color
                        let iconColor;
                        if (movementStatus == 1) {
                            iconColor = 'green';
                        } else if (movementStatus == 0 && ignitionStatus == 1) {
                            iconColor = 'yellow';
                        } else {
                            iconColor = 'red';
                        }

                        // Create wrapper container
                        const wrapperElement = document.createElement('div');
                        wrapperElement.className = 'relative flex items-center justify-center';

                        // Create outer ring
                        const outerRing = document.createElement('div');
                        outerRing.className = 'absolute w-[75px] h-[75px] rounded-full';
                        outerRing.style.background = 'rgba(245, 69, 25, 0.2)';
                        outerRing.style.animation = 'spin 5s linear infinite';

                        // Create inner ring
                        const innerRing = document.createElement('div');
                        innerRing.className = 'absolute w-[65px] h-[65px] rounded-full';
                        innerRing.style.border = '2px dashed #F54619';
                        innerRing.style.animation = 'spin 5s linear infinite reverse';

                        // Main vehicle icon
                        const iconImg = document.createElement('img');
                        iconImg.src =
                            `{{ asset('assets/images/icons/${seletedVehicleIcon}/${iconColor}.png') }}`;
                        iconImg.className = 'h-[55px] w-auto z-10';
                        iconImg.style.transform = `rotate(${angle || 0}deg)`;

                        // Add animation styles
                        const style = document.createElement('style');
                        style.textContent = `
                                @keyframes spin {
                                    from { transform: rotate(0deg); }
                                    to { transform: rotate(360deg); }
                                }
                            `;
                        document.head.appendChild(style);

                        // Assemble the marker elements
                        wrapperElement.appendChild(outerRing);
                        wrapperElement.appendChild(innerRing);
                        wrapperElement.appendChild(iconImg);


                        historyMarker.content = wrapperElement;

                        // Center the map on the moving marker
                        map.panTo(markerPosition);


                    }
                }

                // Update the seekbar
                updateSeekbar();
            } else {
                clearInterval(playInterval);
                playInterval = null;
            }
        }


        // Update the seekbar when the marker moves automatically
        function updateSeekbar() {
            let seekbar = document.getElementById('seekbar');
            seekbar.value = (currentHistoryIndex / (historyData.length - 1)) * 100;

            // Update the current time display
            let fullTime = historyData[currentHistoryIndex]?.last_update;
            let speed = historyData[currentHistoryIndex]?.speed;
            document.getElementById('currentTime').innerText = fullTime;
            // document.getElementById('speed').innerHTML = `${speed} km/h`;

            window.dispatchEvent(new CustomEvent('speed-updated', {
                detail: speed
            }));


            // Corrected fuel level calculation
            let fuelLevel = null;
            const currentData = historyData[currentHistoryIndex];

            if (currentData) {
                // Check each possible fuel level property in order of priority
                if (currentData['37'] !== undefined && currentData['37'] !== null) {
                    fuelLevel = currentData['37'];
                } else if (currentData['87'] !== undefined && currentData['87'] !== null) {
                    fuelLevel = currentData['87'];
                } else if (currentData['89'] !== undefined && currentData['89'] !== null) {
                    fuelLevel = currentData['89'];
                } else if (currentData['48'] !== undefined && currentData['48'] !== null) {
                    fuelLevel = currentData['48'];
                }

                // Only dispatch the event if we have a valid fuel level
                if (fuelLevel !== null) {
                    window.dispatchEvent(new CustomEvent('fuel-level-updated', {
                        detail: fuelLevel
                    }));
                }
            }
        }

        window.onload = initializeMap;

        // Load the map when the API is ready
        // initializeMap();

        function updateGeofencesOnMap(geofencesData, editable = false) {
            // Clear existing geofences
            drawnGeofences.forEach(function(geofence) {
                geofence.setMap(null);
            });
            drawnGeofences = [];

            geofencesData.forEach(function(geofenceData) {
                let geofence;

                // Handle circle geofences
                if (geofenceData?.geofence_data?.type === 'circle') {
                    geofence = new google.maps.Circle({
                        center: geofenceData?.geofence_data?.geofence,
                        radius: geofenceData?.geofence_data?.radius,
                        fillColor: 'green',
                        fillOpacity: 0.35,
                        strokeColor: 'green',
                        strokeWeight: 2,
                        editable: editable,
                        clickable: true,
                    });
                }

                // Handle polygon geofences
                else if (geofenceData?.geofence_data?.type === 'polygon') {
                    geofence = new google.maps.Polygon({
                        paths: geofenceData?.geofence_data?.geofence,
                        fillColor: 'green',
                        fillOpacity: 0.35,
                        strokeColor: 'green',
                        strokeWeight: 2,
                        editable: editable,
                        clickable: true,
                    });
                }

                // Handle rectangle geofences
                else if (geofenceData?.geofence_data?.type === 'rectangle') {
                    geofence = new google.maps.Rectangle({
                        bounds: geofenceData?.geofence_data?.geofence,
                        fillColor: 'green',
                        fillOpacity: 0.35,
                        strokeColor: 'green',
                        strokeWeight: 2,
                        editable: editable,
                        clickable: true,
                    });
                }

                // Add geofence to the map and keep track of it
                if (geofence) {
                    geofence.setMap(map);
                    drawnGeofences.push(geofence);
                }
            });
        }
    </script>

    <script async
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCbWYIwqkYWWpDIEsiA7sjLmyXolx0HExA&callback=initializeMap&v=weekly&libraries=marker,geometry">
    </script>
@endpush
